import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import os

def fetch_nifty_data_comprehensive():
    """
    Comprehensive Nifty data fetcher with multiple options
    """
    
    print("🔥 Enhanced Nifty Data Fetcher 🔥")
    print("=" * 50)
    
    # Get current date
    today = datetime.now()
    
    # Define different time periods
    periods = {
        '1': ('1 Year', '1y'),
        '2': ('2 Years', '2y'),
        '3': ('3 Years', '3y'),
        '4': ('5 Years', '5y'),
        '5': ('10 Years', '10y'),
        '6': ('Max Available', 'max')
    }
    
    print("Select time period:")
    for key, (name, period) in periods.items():
        print(f"{key}. {name}")
    
    choice = input("\nEnter your choice (1-6): ").strip()
    
    if choice not in periods:
        print("Invalid choice. Using 5 years as default.")
        choice = '4'
    
    period_name, period_code = periods[choice]
    
    try:
        print(f"\n📊 Fetching {period_name} of Nifty data...")
        
        # Fetch data using yfinance
        nifty = yf.Ticker("^NSEI")
        hist_data = nifty.history(period=period_code)
        
        if hist_data.empty:
            print("❌ No data retrieved. Please check your internet connection.")
            return None
        
        # Reset index to make Date a column
        hist_data.reset_index(inplace=True)
        
        # Format data exactly like Yahoo Finance
        formatted_data = pd.DataFrame({
            'Date': hist_data['Date'].dt.strftime('%b %d, %Y'),
            'Open': hist_data['Open'].round(2),
            'High': hist_data['High'].round(2),
            'Low': hist_data['Low'].round(2),
            'Close': hist_data['Close'].round(2),
            'Adj Close': hist_data['Close'].round(2),  # For Nifty, Close and Adj Close are same
            'Volume': hist_data['Volume'].fillna(0).astype(int)
        })
        
        # Sort by date (most recent first, like Yahoo Finance)
        formatted_data = formatted_data.iloc[::-1].reset_index(drop=True)
        
        # Generate filename
        filename = f"Nifty_{period_name.replace(' ', '_')}_{today.strftime('%Y%m%d_%H%M%S')}.csv"
        
        # Save to CSV
        formatted_data.to_csv(filename, index=False)
        
        # Display summary
        print(f"\n✅ Data successfully saved to: {filename}")
        print(f"📈 Total records: {len(formatted_data):,}")
        print(f"📅 Date range: {formatted_data['Date'].iloc[-1]} to {formatted_data['Date'].iloc[0]}")
        
        # Display recent data
        print(f"\n📋 Most Recent 10 Records:")
        print("-" * 80)
        display_data = formatted_data.head(10).copy()
        for col in ['Open', 'High', 'Low', 'Close', 'Adj Close']:
            display_data[col] = display_data[col].apply(lambda x: f"{x:,.2f}")
        display_data['Volume'] = display_data['Volume'].apply(lambda x: f"{x:,}")
        print(display_data.to_string(index=False))
        
        # Display statistics
        print(f"\n📊 Market Statistics:")
        print("-" * 30)
        print(f"🔺 Highest Close: ₹{formatted_data['Close'].max():,.2f}")
        print(f"🔻 Lowest Close:  ₹{formatted_data['Close'].min():,.2f}")
        print(f"📊 Average Close: ₹{formatted_data['Close'].mean():,.2f}")
        print(f"📈 Current Close: ₹{formatted_data['Close'].iloc[0]:,.2f}")
        print(f"📊 Average Volume: {formatted_data['Volume'].mean():,.0f}")
        
        # Calculate some basic metrics
        if len(formatted_data) > 1:
            latest_close = formatted_data['Close'].iloc[0]
            previous_close = formatted_data['Close'].iloc[1]
            change = latest_close - previous_close
            change_pct = (change / previous_close) * 100
            
            print(f"📈 Latest Change: ₹{change:+,.2f} ({change_pct:+.2f}%)")
        
        return formatted_data, filename
        
    except Exception as e:
        print(f"❌ Error fetching data: {str(e)}")
        return None, None

def fetch_custom_date_range():
    """
    Fetch data for custom date range
    """
    print("\n📅 Custom Date Range Fetcher")
    print("-" * 30)
    
    start_date = input("Enter start date (YYYY-MM-DD): ").strip()
    end_date = input("Enter end date (YYYY-MM-DD): ").strip()
    
    try:
        # Validate dates
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        if start_dt >= end_dt:
            print("❌ Start date must be before end date.")
            return None, None
        
        print(f"\n📊 Fetching data from {start_date} to {end_date}...")
        
        nifty = yf.Ticker("^NSEI")
        hist_data = nifty.history(start=start_date, end=end_date)
        
        if hist_data.empty:
            print("❌ No data found for the specified date range.")
            return None, None
        
        hist_data.reset_index(inplace=True)
        
        formatted_data = pd.DataFrame({
            'Date': hist_data['Date'].dt.strftime('%b %d, %Y'),
            'Open': hist_data['Open'].round(2),
            'High': hist_data['High'].round(2),
            'Low': hist_data['Low'].round(2),
            'Close': hist_data['Close'].round(2),
            'Adj Close': hist_data['Close'].round(2),
            'Volume': hist_data['Volume'].fillna(0).astype(int)
        })
        
        formatted_data = formatted_data.iloc[::-1].reset_index(drop=True)
        
        filename = f"Nifty_Custom_{start_date}_to_{end_date}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        formatted_data.to_csv(filename, index=False)
        
        print(f"✅ Custom range data saved to: {filename}")
        print(f"📈 Total records: {len(formatted_data):,}")
        
        return formatted_data, filename
        
    except ValueError:
        print("❌ Invalid date format. Please use YYYY-MM-DD format.")
        return None, None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None, None

def main():
    """
    Main function
    """
    print("🚀 Welcome to Enhanced Nifty Data Fetcher!")
    print("This tool fetches Nifty 50 data in Yahoo Finance format.")
    print()
    
    while True:
        print("\n🔧 Options:")
        print("1. Fetch predefined period data")
        print("2. Fetch custom date range data")
        print("3. Exit")
        
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == '1':
            data, filename = fetch_nifty_data_comprehensive()
            if data is not None:
                print(f"\n🎉 Success! Your data is ready in: {filename}")
        
        elif choice == '2':
            data, filename = fetch_custom_date_range()
            if data is not None:
                print(f"\n🎉 Success! Your custom data is ready in: {filename}")
        
        elif choice == '3':
            print("\n👋 Thank you for using Enhanced Nifty Data Fetcher!")
            break
        
        else:
            print("❌ Invalid choice. Please select 1, 2, or 3.")
        
        # Ask if user wants to continue
        if choice in ['1', '2']:
            continue_choice = input("\nDo you want to fetch more data? (y/n): ").strip().lower()
            if continue_choice != 'y':
                print("\n👋 Thank you for using Enhanced Nifty Data Fetcher!")
                break

if __name__ == "__main__":
    main()
