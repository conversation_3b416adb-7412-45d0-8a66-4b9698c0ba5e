import yfinance as yf
import pandas as pd
from datetime import datetime

def fetch_nifty_5min_max():
    """
    Fetch maximum available Nifty data in 5-minute timeframe
    """
    
    print("🚀 Fetching Maximum Available Nifty 5-Minute Data")
    print("=" * 55)
    
    try:
        # Fetch Nifty 50 data with 5-minute interval
        print("📊 Fetching 5-minute interval data...")
        
        nifty = yf.Ticker("^NSEI")
        
        # Try different periods to get maximum data
        periods_to_try = ['max', '2y', '1y', '6mo', '3mo', '1mo']
        
        hist_data = None
        period_used = None
        
        for period in periods_to_try:
            try:
                print(f"   Trying period: {period}")
                hist_data = nifty.history(period=period, interval="5m")
                if not hist_data.empty:
                    period_used = period
                    print(f"   ✅ Success with period: {period}")
                    break
                else:
                    print(f"   ❌ No data for period: {period}")
            except Exception as e:
                print(f"   ❌ Error with period {period}: {str(e)}")
                continue
        
        if hist_data is None or hist_data.empty:
            print("❌ Failed to fetch any 5-minute data")
            return None
        
        # Reset index to make Datetime a column
        hist_data.reset_index(inplace=True)
        
        # Format data like Yahoo Finance
        formatted_data = pd.DataFrame({
            'Datetime': hist_data['Datetime'].dt.strftime('%Y-%m-%d %H:%M:%S'),
            'Date': hist_data['Datetime'].dt.strftime('%b %d, %Y'),
            'Time': hist_data['Datetime'].dt.strftime('%H:%M'),
            'Open': hist_data['Open'].round(2),
            'High': hist_data['High'].round(2),
            'Low': hist_data['Low'].round(2),
            'Close': hist_data['Close'].round(2),
            'Adj Close': hist_data['Close'].round(2),
            'Volume': hist_data['Volume'].fillna(0).astype(int)
        })
        
        # Sort by datetime (most recent first)
        formatted_data = formatted_data.iloc[::-1].reset_index(drop=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"Nifty_5Min_Max_Available_{timestamp}.csv"
        
        # Save to CSV
        formatted_data.to_csv(filename, index=False)
        
        # Display summary
        print(f"\n✅ SUCCESS! 5-Minute Data Saved")
        print("=" * 40)
        print(f"📁 File: {filename}")
        print(f"📈 Total Records: {len(formatted_data):,}")
        print(f"📅 Date Range: {formatted_data['Date'].iloc[-1]} to {formatted_data['Date'].iloc[0]}")
        print(f"⏰ Time Range: {formatted_data['Time'].iloc[-1]} to {formatted_data['Time'].iloc[0]}")
        print(f"🔧 Period Used: {period_used}")
        
        # Show first few records
        print(f"\n📋 First 5 Records:")
        print("-" * 80)
        display_cols = ['Datetime', 'Open', 'High', 'Low', 'Close', 'Volume']
        print(formatted_data[display_cols].head(5).to_string(index=False))
        
        # Show basic stats
        print(f"\n📊 Basic Statistics:")
        print(f"   Highest: ₹{formatted_data['High'].max():,.2f}")
        print(f"   Lowest: ₹{formatted_data['Low'].min():,.2f}")
        print(f"   Latest Close: ₹{formatted_data['Close'].iloc[0]:,.2f}")
        print(f"   Average Volume: {formatted_data['Volume'].mean():,.0f}")
        
        return formatted_data, filename
        
    except Exception as e:
        print(f"❌ Error fetching 5-minute data: {str(e)}")
        return None, None

def main():
    """
    Main function - just fetch, no analysis
    """
    print("🔥 Nifty 5-Minute Data Fetcher")
    print("Fetching maximum available data...")
    print()
    
    data, filename = fetch_nifty_5min_max()
    
    if data is not None:
        print(f"\n🎉 COMPLETE! Your 5-minute Nifty data is ready!")
        print(f"📁 File saved as: {filename}")
        print(f"📊 Total 5-minute candles: {len(data):,}")
        print("\n✅ Ready for your analysis!")
    else:
        print("\n❌ Failed to fetch 5-minute data")
        print("This might be due to:")
        print("- Yahoo Finance API limitations for intraday data")
        print("- Network connectivity issues")
        print("- Data availability restrictions")

if __name__ == "__main__":
    main()
