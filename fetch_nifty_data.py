try:
    import yfinance as yf
    import pandas as pd
    from datetime import datetime, timedelta
    import numpy as np
    print("All required packages imported successfully!")
except ImportError as e:
    print(f"Missing package: {e}")
    print("Please install required packages using:")
    print("pip install yfinance pandas numpy")
    exit(1)

def fetch_nifty_5_years():
    """
    Fetch last 5 years of daily Nifty data from Yahoo Finance
    """
    
    # Calculate date range (last 5 years)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=5*365 + 2)  # Adding 2 days for leap years
    
    print(f"Fetching Nifty data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    try:
        # Fetch Nifty 50 data (^NSEI is the Yahoo Finance symbol for Nifty 50)
        nifty = yf.Ticker("^NSEI")
        
        # Get historical data
        hist_data = nifty.history(start=start_date, end=end_date)
        
        if hist_data.empty:
            print("No data retrieved. Trying alternative approach...")
            # Try with period parameter
            hist_data = nifty.history(period="5y")
        
        # Reset index to make Date a column
        hist_data.reset_index(inplace=True)
        
        # Format the data to match Yahoo Finance format
        formatted_data = pd.DataFrame({
            'Date': hist_data['Date'].dt.strftime('%b %d, %Y'),
            'Open': hist_data['Open'].round(2),
            'High': hist_data['High'].round(2),
            'Low': hist_data['Low'].round(2),
            'Close': hist_data['Close'].round(2),
            'Adj Close': hist_data['Close'].round(2),  # For Nifty, Close and Adj Close are usually same
            'Volume': hist_data['Volume'].astype(int)
        })
        
        # Sort by date (most recent first, like Yahoo Finance)
        formatted_data = formatted_data.sort_values('Date', ascending=False)
        formatted_data.reset_index(drop=True, inplace=True)
        
        # Save to CSV
        csv_filename = f"Nifty_5Years_Daily_Data_{datetime.now().strftime('%Y%m%d')}.csv"
        formatted_data.to_csv(csv_filename, index=False)
        
        print(f"\nData successfully saved to: {csv_filename}")
        print(f"Total records: {len(formatted_data)}")
        print(f"Date range: {formatted_data['Date'].iloc[-1]} to {formatted_data['Date'].iloc[0]}")
        
        # Display first few rows
        print("\nFirst 10 rows of data:")
        print(formatted_data.head(10).to_string(index=False))
        
        # Display basic statistics
        print(f"\nBasic Statistics:")
        print(f"Highest Close: {formatted_data['Close'].max():.2f}")
        print(f"Lowest Close: {formatted_data['Close'].min():.2f}")
        print(f"Average Volume: {formatted_data['Volume'].mean():,.0f}")
        
        return formatted_data
        
    except Exception as e:
        print(f"Error fetching data: {str(e)}")
        print("Trying alternative method...")
        
        # Alternative method using different period specification
        try:
            hist_data = yf.download("^NSEI", period="5y", interval="1d")
            
            if not hist_data.empty:
                hist_data.reset_index(inplace=True)
                
                formatted_data = pd.DataFrame({
                    'Date': hist_data['Date'].dt.strftime('%b %d, %Y'),
                    'Open': hist_data['Open'].round(2),
                    'High': hist_data['High'].round(2),
                    'Low': hist_data['Low'].round(2),
                    'Close': hist_data['Close'].round(2),
                    'Adj Close': hist_data['Close'].round(2),
                    'Volume': hist_data['Volume'].astype(int)
                })
                
                formatted_data = formatted_data.sort_values('Date', ascending=False)
                formatted_data.reset_index(drop=True, inplace=True)
                
                csv_filename = f"Nifty_5Years_Daily_Data_{datetime.now().strftime('%Y%m%d')}.csv"
                formatted_data.to_csv(csv_filename, index=False)
                
                print(f"Alternative method successful! Data saved to: {csv_filename}")
                return formatted_data
            
        except Exception as e2:
            print(f"Alternative method also failed: {str(e2)}")
            return None

def fetch_nifty_custom_range(start_date_str, end_date_str):
    """
    Fetch Nifty data for custom date range
    Format: 'YYYY-MM-DD'
    """
    try:
        nifty = yf.Ticker("^NSEI")
        hist_data = nifty.history(start=start_date_str, end=end_date_str)
        
        hist_data.reset_index(inplace=True)
        
        formatted_data = pd.DataFrame({
            'Date': hist_data['Date'].dt.strftime('%b %d, %Y'),
            'Open': hist_data['Open'].round(2),
            'High': hist_data['High'].round(2),
            'Low': hist_data['Low'].round(2),
            'Close': hist_data['Close'].round(2),
            'Adj Close': hist_data['Close'].round(2),
            'Volume': hist_data['Volume'].astype(int)
        })
        
        formatted_data = formatted_data.sort_values('Date', ascending=False)
        formatted_data.reset_index(drop=True, inplace=True)
        
        csv_filename = f"Nifty_Custom_Range_{start_date_str}_to_{end_date_str}.csv"
        formatted_data.to_csv(csv_filename, index=False)
        
        print(f"Custom range data saved to: {csv_filename}")
        return formatted_data
        
    except Exception as e:
        print(f"Error fetching custom range data: {str(e)}")
        return None

if __name__ == "__main__":
    print("Nifty Data Fetcher")
    print("==================")
    
    # Fetch 5 years data
    data = fetch_nifty_5_years()
    
    if data is not None:
        print("\n✅ Data fetch completed successfully!")
        
        # Ask user if they want custom range
        print("\nWould you like to fetch data for a custom date range as well? (y/n)")
        choice = input().lower().strip()
        
        if choice == 'y':
            print("Enter start date (YYYY-MM-DD): ")
            start_date = input().strip()
            print("Enter end date (YYYY-MM-DD): ")
            end_date = input().strip()
            
            custom_data = fetch_nifty_custom_range(start_date, end_date)
            if custom_data is not None:
                print("✅ Custom range data also fetched successfully!")
    else:
        print("❌ Failed to fetch data. Please check your internet connection and try again.")
