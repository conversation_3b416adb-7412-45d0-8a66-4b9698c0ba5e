import pandas as pd
import numpy as np
from datetime import datetime
import os

class NiftyDataAnalyzer:
    """
    Nifty Data Analyzer based on your ground truth methodology
    """
    
    def __init__(self, csv_file):
        """
        Initialize with CSV file
        """
        self.csv_file = csv_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """
        Load data from CSV file
        """
        try:
            self.data = pd.read_csv(self.csv_file)
            # Convert Date column to datetime for easier manipulation
            self.data['Date'] = pd.to_datetime(self.data['Date'])
            # Sort by date (oldest first for analysis)
            self.data = self.data.sort_values('Date').reset_index(drop=True)
            print(f"✅ Loaded {len(self.data)} records from {self.csv_file}")
        except Exception as e:
            print(f"❌ Error loading data: {str(e)}")
    
    def calculate_gaps(self):
        """
        Calculate gap analysis like your methodology
        """
        if self.data is None:
            return None
        
        gaps = []
        for i in range(1, len(self.data)):
            prev_close = self.data.iloc[i-1]['Close']
            curr_open = self.data.iloc[i]['Open']
            gap = curr_open - prev_close
            gap_pct = (gap / prev_close) * 100
            
            # Classify gap based on your methodology
            if abs(gap_pct) < 0.5:
                gap_type = "No Gap"
            elif abs(gap_pct) < 1.5:
                gap_type = "Small Gap"
            elif abs(gap_pct) < 3.0:
                gap_type = "Medium Gap"
            else:
                gap_type = "Large Gap"
            
            direction = "Gap Up" if gap > 0 else "Gap Down" if gap < 0 else "No Gap"
            
            gaps.append({
                'Date': self.data.iloc[i]['Date'],
                'Previous_Close': prev_close,
                'Current_Open': curr_open,
                'Gap_Points': gap,
                'Gap_Percentage': gap_pct,
                'Gap_Type': gap_type,
                'Direction': direction
            })
        
        return pd.DataFrame(gaps)
    
    def identify_candlestick_patterns(self):
        """
        Identify candlestick patterns based on your methodology
        """
        if self.data is None:
            return None
        
        patterns = []
        
        for i in range(len(self.data)):
            row = self.data.iloc[i]
            open_price = row['Open']
            high_price = row['High']
            low_price = row['Low']
            close_price = row['Close']
            
            # Calculate body and wick sizes
            body_size = abs(close_price - open_price)
            upper_wick = high_price - max(open_price, close_price)
            lower_wick = min(open_price, close_price) - low_price
            total_range = high_price - low_price
            
            # Pattern identification based on your logic
            pattern_name = "Regular Candle"
            pattern_signal = "Neutral"
            
            if total_range > 0:
                body_ratio = body_size / total_range
                upper_wick_ratio = upper_wick / total_range
                lower_wick_ratio = lower_wick / total_range
                
                # Spinning Top (small body, large wicks)
                if body_ratio < 0.3 and (upper_wick_ratio > 0.3 or lower_wick_ratio > 0.3):
                    pattern_name = "Spinning Top"
                    pattern_signal = "Indecision"
                
                # Hammer/Hanging Man (small body, long lower wick)
                elif body_ratio < 0.3 and lower_wick_ratio > 0.6:
                    if close_price > open_price:
                        pattern_name = "Hammer"
                        pattern_signal = "Bullish"
                    else:
                        pattern_name = "Hanging Man"
                        pattern_signal = "Bearish"
                
                # Inverted Hammer (small body, long upper wick)
                elif body_ratio < 0.3 and upper_wick_ratio > 0.6:
                    pattern_name = "Inverted Hammer"
                    pattern_signal = "Bearish" if i > 0 and self.data.iloc[i-1]['Close'] < close_price else "Neutral"
                
                # Strong Bullish/Bearish candles
                elif body_ratio > 0.7:
                    if close_price > open_price:
                        pattern_name = "Strong Bullish"
                        pattern_signal = "Bullish"
                    else:
                        pattern_name = "Strong Bearish"
                        pattern_signal = "Bearish"
            
            patterns.append({
                'Date': row['Date'],
                'Pattern': pattern_name,
                'Signal': pattern_signal,
                'Body_Size': body_size,
                'Upper_Wick': upper_wick,
                'Lower_Wick': lower_wick,
                'Body_Ratio': body_ratio if total_range > 0 else 0,
                'OHLC': f"O:{open_price:.2f} H:{high_price:.2f} L:{low_price:.2f} C:{close_price:.2f}"
            })
        
        return pd.DataFrame(patterns)
    
    def find_support_resistance_levels(self, lookback_period=20):
        """
        Find support and resistance levels based on your SR interchange methodology
        """
        if self.data is None:
            return None
        
        sr_levels = []
        
        for i in range(lookback_period, len(self.data)):
            # Get recent data window
            window = self.data.iloc[i-lookback_period:i+1]
            
            current_price = self.data.iloc[i]['Close']
            
            # Find local highs and lows
            highs = window['High'].values
            lows = window['Low'].values
            
            # Resistance levels (recent highs)
            resistance_levels = []
            for j in range(1, len(highs)-1):
                if highs[j] > highs[j-1] and highs[j] > highs[j+1]:
                    resistance_levels.append(highs[j])
            
            # Support levels (recent lows)
            support_levels = []
            for j in range(1, len(lows)-1):
                if lows[j] < lows[j-1] and lows[j] < lows[j+1]:
                    support_levels.append(lows[j])
            
            # Find nearest support and resistance
            nearest_resistance = min(resistance_levels, key=lambda x: abs(x - current_price)) if resistance_levels else None
            nearest_support = min(support_levels, key=lambda x: abs(x - current_price)) if support_levels else None
            
            sr_levels.append({
                'Date': self.data.iloc[i]['Date'],
                'Current_Price': current_price,
                'Nearest_Support': nearest_support,
                'Nearest_Resistance': nearest_resistance,
                'Support_Distance': (current_price - nearest_support) if nearest_support else None,
                'Resistance_Distance': (nearest_resistance - current_price) if nearest_resistance else None
            })
        
        return pd.DataFrame(sr_levels)
    
    def generate_analysis_report(self):
        """
        Generate comprehensive analysis report
        """
        if self.data is None:
            print("❌ No data loaded.")
            return
        
        print("\n" + "="*60)
        print("📊 NIFTY DATA ANALYSIS REPORT")
        print("="*60)
        
        # Basic statistics
        print(f"\n📈 BASIC STATISTICS:")
        print(f"   Total Records: {len(self.data):,}")
        print(f"   Date Range: {self.data['Date'].min().strftime('%b %d, %Y')} to {self.data['Date'].max().strftime('%b %d, %Y')}")
        print(f"   Highest Close: ₹{self.data['Close'].max():,.2f}")
        print(f"   Lowest Close: ₹{self.data['Close'].min():,.2f}")
        print(f"   Average Close: ₹{self.data['Close'].mean():,.2f}")
        print(f"   Current Close: ₹{self.data['Close'].iloc[-1]:,.2f}")
        
        # Gap analysis
        print(f"\n🔄 GAP ANALYSIS:")
        gaps_df = self.calculate_gaps()
        if gaps_df is not None:
            gap_summary = gaps_df['Gap_Type'].value_counts()
            print(f"   Total Gap Days: {len(gaps_df):,}")
            for gap_type, count in gap_summary.items():
                print(f"   {gap_type}: {count} ({count/len(gaps_df)*100:.1f}%)")
            
            # Recent significant gaps
            significant_gaps = gaps_df[gaps_df['Gap_Type'].isin(['Medium Gap', 'Large Gap'])].tail(5)
            if not significant_gaps.empty:
                print(f"\n   Recent Significant Gaps:")
                for _, gap in significant_gaps.iterrows():
                    print(f"   {gap['Date'].strftime('%b %d, %Y')}: {gap['Direction']} {gap['Gap_Points']:+.2f} ({gap['Gap_Percentage']:+.2f}%)")
        
        # Candlestick patterns
        print(f"\n🕯️ CANDLESTICK PATTERNS:")
        patterns_df = self.identify_candlestick_patterns()
        if patterns_df is not None:
            pattern_summary = patterns_df['Pattern'].value_counts()
            print(f"   Pattern Distribution:")
            for pattern, count in pattern_summary.head(5).items():
                print(f"   {pattern}: {count} ({count/len(patterns_df)*100:.1f}%)")
            
            # Recent interesting patterns
            interesting_patterns = patterns_df[~patterns_df['Pattern'].isin(['Regular Candle'])].tail(5)
            if not interesting_patterns.empty:
                print(f"\n   Recent Notable Patterns:")
                for _, pattern in interesting_patterns.iterrows():
                    print(f"   {pattern['Date'].strftime('%b %d, %Y')}: {pattern['Pattern']} ({pattern['Signal']})")
        
        # Volatility analysis
        print(f"\n📊 VOLATILITY ANALYSIS:")
        self.data['Daily_Range'] = self.data['High'] - self.data['Low']
        self.data['Daily_Change'] = self.data['Close'] - self.data['Open']
        self.data['Daily_Change_Pct'] = (self.data['Daily_Change'] / self.data['Open']) * 100
        
        print(f"   Average Daily Range: ₹{self.data['Daily_Range'].mean():.2f}")
        print(f"   Average Daily Change: {self.data['Daily_Change_Pct'].mean():+.2f}%")
        print(f"   Volatility (Std Dev): {self.data['Daily_Change_Pct'].std():.2f}%")
        
        # Recent performance
        if len(self.data) >= 30:
            recent_30d = self.data.tail(30)
            performance_30d = ((recent_30d['Close'].iloc[-1] / recent_30d['Close'].iloc[0]) - 1) * 100
            print(f"   Last 30 Days Performance: {performance_30d:+.2f}%")
        
        print("\n" + "="*60)
        print("✅ Analysis Complete!")

def main():
    """
    Main function to run the analyzer
    """
    print("🔍 Nifty Data Analyzer")
    print("Based on your ground truth methodology")
    print("-" * 40)
    
    # List available CSV files
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and 'Nifty' in f]
    
    if not csv_files:
        print("❌ No Nifty CSV files found in current directory.")
        print("Please run the data fetcher first to get Nifty data.")
        return
    
    print("📁 Available CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"{i}. {file}")
    
    try:
        choice = int(input(f"\nSelect file (1-{len(csv_files)}): ")) - 1
        if 0 <= choice < len(csv_files):
            selected_file = csv_files[choice]
            
            # Initialize analyzer
            analyzer = NiftyDataAnalyzer(selected_file)
            
            # Generate report
            analyzer.generate_analysis_report()
            
        else:
            print("❌ Invalid choice.")
    
    except ValueError:
        print("❌ Please enter a valid number.")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
