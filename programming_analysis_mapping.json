{"metadata": {"created_at": "2025-08-14T13:40:03.639134", "purpose": "Convert natural language analysis to programming terms for AI training", "approach": "Treat user analysis as ground truth, map to CSV data", "total_observations": 18}, "jan_1_2018": {"programming_data": {"observation_id": "JAN_01_2018_PROGRAMMING", "date": "2018-01-01", "your_analysis": "Market opens 10 points gap up, sideways to bearish till 1:20, bullish 1:25-2:20, bearish from 2:25, inverted hammer 2:40, hanging man 2:45, hanging man bottom 3:05, parallel channel 9:25-2:45, channel break 2:50 with big IFC", "programming_formulas": {"gap_calculation": "open_price - previous_close", "trend_direction": "end_price - start_price", "trend_strength": "abs(price_change) / start_price * 100", "inverted_hammer": "upper_shadow > body_size * 2 and lower_shadow < body_size", "hanging_man": "lower_shadow > body_size * 2 and upper_shadow < body_size", "channel_break": "candle_low < channel_support or candle_high > channel_resistance", "big_candle": "candle_range > average_range * 1.5"}, "mapped_observations": [{"claim": "Market opens around 10 points gap up from yesterday close 10467.40", "type": "gap_analysis", "data": {"datetime": "2018-01-01 09:20:00", "open": 10523.7999, "high": 10534.7999, "low": 10523.5, "close": 10528.2999, "csv_row": 0}, "programming_logic": "gap_points = open_price - 10467.40", "validation_formula": "abs(gap_points - 10) <= 5"}, {"claim": "remained sideways to bearish till the close of 1:20", "type": "trend_phase", "data": {"start_price": 10528.2999, "end_price": 10507.3999, "high": 10537.7999, "low": 10506.5, "net_move": -20.899999999999636, "candle_count": 49, "csv_rows": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48]}, "programming_logic": "trend = \"bearish\" if end_price < start_price else \"bullish\"", "validation_formula": "net_move < 0"}, {"claim": "from 1:25 it give one bullish move till 2:20 close", "type": "trend_phase", "data": {"start_price": 10512.1, "end_price": 10529.3999, "high": 10530.7, "low": 10504.2, "net_move": 17.29989999999998, "candle_count": 12, "csv_rows": [49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60]}, "programming_logic": "bullish_move = end_price > start_price", "validation_formula": "net_move > 0"}, {"claim": "from 2:25 candle it started bearish move", "type": "trend_phase", "data": {"start_price": 10527.7999, "end_price": 10435.0, "high": 10531.7, "low": 10423.1, "net_move": -92.79989999999998, "candle_count": 13, "csv_rows": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73]}, "programming_logic": "bearish_move = end_price < start_price", "validation_formula": "net_move < 0"}, {"claim": "inverted hammer at 2:40", "type": "candlestick_pattern", "data": {"datetime": "2018-01-01 14:40:00", "open": 10524.0, "high": 10530.0, "low": 10521.2999, "close": 10525.3999, "csv_row": 64}, "programming_logic": "upper_shadow = high - max(open, close); body = abs(close - open)", "validation_formula": "upper_shadow > body * 2"}, {"claim": "hanging man at 2:45", "type": "candlestick_pattern", "data": {"datetime": "2018-01-01 14:45:00", "open": 10525.1, "high": 10528.3999, "low": 10519.5, "close": 10519.7999, "csv_row": 65}, "programming_logic": "lower_shadow = min(open, close) - low; body = abs(close - open)", "validation_formula": "lower_shadow > body * 2"}, {"claim": "hanging man at 3:05 (bottom)", "type": "candlestick_pattern", "data": {"datetime": "2018-01-01 15:05:00", "open": 10440.2, "high": 10444.7, "low": 10423.1, "close": 10433.8999, "csv_row": 69}, "programming_logic": "is_bottom = candle_low == day_low", "validation_formula": "lower_shadow > body * 2 and is_bottom"}, {"claim": "parallel channel from 9:25 to 2:45", "type": "chart_pattern", "data": {"start_price": 10522.7999, "end_price": 10519.7999, "high": 10537.7999, "low": 10504.2, "net_move": -3.0, "candle_count": 65, "csv_rows": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]}, "programming_logic": "channel_top = max(highs); channel_bottom = min(lows)", "validation_formula": "price_oscillates_between_levels"}, {"claim": "2:50 candle breaks parallel channel with big IFC", "type": "pattern_break", "data": {"datetime": "2018-01-01 14:50:00", "open": 10519.7999, "high": 10523.1, "low": 10487.8999, "close": 10488.3999, "csv_row": 66}, "programming_logic": "candle_range = high - low; avg_range = average_candle_range", "validation_formula": "candle_range > avg_range * 1.5"}]}, "ai_training_structure": {"training_data": {"observation_id": "JAN_01_2018_PROGRAMMING", "date": "2018-01-01", "analyst_methodology": "Market opens 10 points gap up, sideways to bearish till 1:20, bullish 1:25-2:20, bearish from 2:25, inverted hammer 2:40, hanging man 2:45, hanging man bottom 3:05, parallel channel 9:25-2:45, channel break 2:50 with big IFC", "feature_extraction": {"gap_analysis": [{"claim": "Market opens around 10 points gap up from yesterday close 10467.40", "data": {"datetime": "2018-01-01 09:20:00", "open": 10523.7999, "high": 10534.7999, "low": 10523.5, "close": 10528.2999, "csv_row": 0}, "logic": "gap_points = open_price - 10467.40", "validation": "abs(gap_points - 10) <= 5"}], "trend_phases": [{"claim": "remained sideways to bearish till the close of 1:20", "data": {"start_price": 10528.2999, "end_price": 10507.3999, "high": 10537.7999, "low": 10506.5, "net_move": -20.899999999999636, "candle_count": 49, "csv_rows": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48]}, "logic": "trend = \"bearish\" if end_price < start_price else \"bullish\"", "validation": "net_move < 0"}, {"claim": "from 1:25 it give one bullish move till 2:20 close", "data": {"start_price": 10512.1, "end_price": 10529.3999, "high": 10530.7, "low": 10504.2, "net_move": 17.29989999999998, "candle_count": 12, "csv_rows": [49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60]}, "logic": "bullish_move = end_price > start_price", "validation": "net_move > 0"}, {"claim": "from 2:25 candle it started bearish move", "data": {"start_price": 10527.7999, "end_price": 10435.0, "high": 10531.7, "low": 10423.1, "net_move": -92.79989999999998, "candle_count": 13, "csv_rows": [61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73]}, "logic": "bearish_move = end_price < start_price", "validation": "net_move < 0"}], "candlestick_patterns": [{"claim": "inverted hammer at 2:40", "data": {"datetime": "2018-01-01 14:40:00", "open": 10524.0, "high": 10530.0, "low": 10521.2999, "close": 10525.3999, "csv_row": 64}, "logic": "upper_shadow = high - max(open, close); body = abs(close - open)", "validation": "upper_shadow > body * 2"}, {"claim": "hanging man at 2:45", "data": {"datetime": "2018-01-01 14:45:00", "open": 10525.1, "high": 10528.3999, "low": 10519.5, "close": 10519.7999, "csv_row": 65}, "logic": "lower_shadow = min(open, close) - low; body = abs(close - open)", "validation": "lower_shadow > body * 2"}, {"claim": "hanging man at 3:05 (bottom)", "data": {"datetime": "2018-01-01 15:05:00", "open": 10440.2, "high": 10444.7, "low": 10423.1, "close": 10433.8999, "csv_row": 69}, "logic": "is_bottom = candle_low == day_low", "validation": "lower_shadow > body * 2 and is_bottom"}], "chart_patterns": [{"claim": "parallel channel from 9:25 to 2:45", "data": {"start_price": 10522.7999, "end_price": 10519.7999, "high": 10537.7999, "low": 10504.2, "net_move": -3.0, "candle_count": 65, "csv_rows": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]}, "logic": "channel_top = max(highs); channel_bottom = min(lows)", "validation": "price_oscillates_between_levels"}, {"claim": "2:50 candle breaks parallel channel with big IFC", "data": {"datetime": "2018-01-01 14:50:00", "open": 10519.7999, "high": 10523.1, "low": 10487.8999, "close": 10488.3999, "csv_row": 66}, "logic": "candle_range = high - low; avg_range = average_candle_range", "validation": "candle_range > avg_range * 1.5"}], "support_resistance": []}, "programming_logic": {"gap_calculation": "open_price - previous_close", "trend_direction": "end_price - start_price", "trend_strength": "abs(price_change) / start_price * 100", "inverted_hammer": "upper_shadow > body_size * 2 and lower_shadow < body_size", "hanging_man": "lower_shadow > body_size * 2 and upper_shadow < body_size", "channel_break": "candle_low < channel_support or candle_high > channel_resistance", "big_candle": "candle_range > average_range * 1.5"}, "validation_rules": {}, "csv_mapping": {"rows_to_fill": [{"csv_row": 0, "observation_type": "gap_analysis", "claim": "Market opens around 10 points gap up from yesterday close 10467.40"}, {"csv_row": null, "observation_type": "trend_phase", "claim": "remained sideways to bearish till the close of 1:20"}, {"csv_row": null, "observation_type": "trend_phase", "claim": "from 1:25 it give one bullish move till 2:20 close"}, {"csv_row": null, "observation_type": "trend_phase", "claim": "from 2:25 candle it started bearish move"}, {"csv_row": 64, "observation_type": "candlestick_pattern", "claim": "inverted hammer at 2:40"}, {"csv_row": 65, "observation_type": "candlestick_pattern", "claim": "hanging man at 2:45"}, {"csv_row": 69, "observation_type": "candlestick_pattern", "claim": "hanging man at 3:05 (bottom)"}, {"csv_row": null, "observation_type": "chart_pattern", "claim": "parallel channel from 9:25 to 2:45"}, {"csv_row": 66, "observation_type": "pattern_break", "claim": "2:50 candle breaks parallel channel with big IFC"}], "column_mappings": {"Observation ID": "observation_id", "Market Condition": "trend_direction", "Pattern Start Time": "start_time", "Pattern End Time": "end_time", "Pattern Name": "pattern_name", "Nature of Pattern": "pattern_type", "Your Reasoning": "original_claim", "Expected Outcome": "predicted_result"}}}}}, "jan_2_2018": {"programming_data": {"observation_id": "JAN_02_2018_PROGRAMMING", "date": "2018-01-02", "your_analysis": "Market formed W pattern previous day 3:00-3:25, opened gap up 45 points with inverted hanging man, bearish till 10:05, bullish engulfing 10:05-10:10, upside till 10:45, inverted H&S 9:40-10:25, 10:05 close as support tested at 2:15, supply zones, demand zones, green hammer 2:35, red spinning top 3:05, inverted hammer 3:25, SR interchange from Jan 1 3:20 wick", "programming_formulas": {"gap_calculation": "open_price - 10435.0", "w_pattern": "double_bottom_with_higher_second_low", "bullish_engulfing": "second_candle_body_engulfs_first", "inverted_h_and_s": "left_shoulder > head < right_shoulder", "support_test": "candle_low <= support_level <= candle_high and close > support_level", "sr_interchange": "level_acts_as_support_then_resistance_then_support", "supply_zone": "area_where_selling_pressure_emerges", "demand_zone": "area_where_buying_pressure_emerges"}, "mapped_observations": [{"claim": "opened gapup around 45 points", "type": "gap_analysis", "data": {"datetime": "2018-01-02 09:15:00", "open": 10479.6, "high": 10495.2, "low": 10469.0, "close": 10477.3999, "csv_row": 74}, "programming_logic": "gap_points = open_price - 10435.0", "validation_formula": "abs(gap_points - 45) <= 5"}, {"claim": "inverted hanging man with long upper shadow", "type": "opening_pattern", "data": {"datetime": "2018-01-02 09:15:00", "open": 10479.6, "high": 10495.2, "low": 10469.0, "close": 10477.3999, "csv_row": 74}, "programming_logic": "upper_shadow = high - max(open, close)", "validation_formula": "upper_shadow > body_size * 3"}, {"claim": "bearish stance till 10:05 candle", "type": "trend_phase", "data": {"start_price": 10477.3999, "end_price": 10412.3999, "high": 10495.2, "low": 10411.5, "net_move": -65.0, "candle_count": 11, "csv_rows": [74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84]}, "programming_logic": "bearish_trend = end_price < start_price", "validation_formula": "net_move < 0"}, {"claim": "bullish engulfing at 10:05-10:10", "type": "reversal_pattern", "data": {"first_candle": {"datetime": "2018-01-02 10:05:00", "open": 10420.1, "high": 10424.7999, "low": 10411.5, "close": 10412.3999, "csv_row": 84}, "second_candle": {"datetime": "2018-01-02 10:10:00", "open": 10412.7, "high": 10428.5, "low": 10409.6, "close": 10428.2999, "csv_row": 85}}, "programming_logic": "engulfing = second_open < first_close and second_close > first_open", "validation_formula": "pattern_validates_engulfing_rules"}, {"claim": "goes upside till 10:45", "type": "trend_phase", "data": {"start_price": 10428.2999, "end_price": 10449.2999, "high": 10455.5, "low": 10409.6, "net_move": 21.0, "candle_count": 8, "csv_rows": [85, 86, 87, 88, 89, 90, 91, 92]}, "programming_logic": "upside_move = end_price > start_price", "validation_formula": "net_move > 0"}, {"claim": "<PERSON> at 2:35", "type": "candlestick_pattern", "data": {"datetime": "2018-01-02 14:35:00", "open": 10414.5, "high": 10415.8999, "low": 10404.6, "close": 10415.5, "csv_row": 138}, "programming_logic": "hammer = lower_shadow > body * 2 and close > open", "validation_formula": "lower_shadow > body_size * 2 and close > open"}, {"claim": "red spinning top at 3:05", "type": "candlestick_pattern", "data": {"datetime": "2018-01-02 15:05:00", "open": 10449.1, "high": 10452.2, "low": 10442.7999, "close": 10446.8999, "csv_row": 144}, "programming_logic": "spinning_top = body < total_range * 0.3 and both_shadows > body", "validation_formula": "body_size < total_range * 0.3"}, {"claim": "inverted hammer at 3:25", "type": "candlestick_pattern", "data": {"datetime": "2018-01-02 15:25:00", "open": 10439.0, "high": 10444.0, "low": 10437.8999, "close": 10440.1, "csv_row": 148}, "programming_logic": "inverted_hammer = upper_shadow > body * 2", "validation_formula": "upper_shadow > body_size * 2"}, {"claim": "10:05 close acted as support, tested at 2:15", "type": "support_resistance", "data": {"support_level": 10412.3999, "test_candle": {"datetime": "2018-01-02 14:15:00", "open": 10423.3999, "high": 10426.2, "low": 10411.8999, "close": 10413.2999, "csv_row": 134}}, "programming_logic": "support_test = candle_low <= support <= candle_high", "validation_formula": "level_acts_as_support"}]}, "ai_training_structure": {"training_data": {"observation_id": "JAN_02_2018_PROGRAMMING", "date": "2018-01-02", "analyst_methodology": "Market formed W pattern previous day 3:00-3:25, opened gap up 45 points with inverted hanging man, bearish till 10:05, bullish engulfing 10:05-10:10, upside till 10:45, inverted H&S 9:40-10:25, 10:05 close as support tested at 2:15, supply zones, demand zones, green hammer 2:35, red spinning top 3:05, inverted hammer 3:25, SR interchange from Jan 1 3:20 wick", "feature_extraction": {"gap_analysis": [{"claim": "opened gapup around 45 points", "data": {"datetime": "2018-01-02 09:15:00", "open": 10479.6, "high": 10495.2, "low": 10469.0, "close": 10477.3999, "csv_row": 74}, "logic": "gap_points = open_price - 10435.0", "validation": "abs(gap_points - 45) <= 5"}], "trend_phases": [{"claim": "bearish stance till 10:05 candle", "data": {"start_price": 10477.3999, "end_price": 10412.3999, "high": 10495.2, "low": 10411.5, "net_move": -65.0, "candle_count": 11, "csv_rows": [74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84]}, "logic": "bearish_trend = end_price < start_price", "validation": "net_move < 0"}, {"claim": "goes upside till 10:45", "data": {"start_price": 10428.2999, "end_price": 10449.2999, "high": 10455.5, "low": 10409.6, "net_move": 21.0, "candle_count": 8, "csv_rows": [85, 86, 87, 88, 89, 90, 91, 92]}, "logic": "upside_move = end_price > start_price", "validation": "net_move > 0"}], "candlestick_patterns": [{"claim": "<PERSON> at 2:35", "data": {"datetime": "2018-01-02 14:35:00", "open": 10414.5, "high": 10415.8999, "low": 10404.6, "close": 10415.5, "csv_row": 138}, "logic": "hammer = lower_shadow > body * 2 and close > open", "validation": "lower_shadow > body_size * 2 and close > open"}, {"claim": "red spinning top at 3:05", "data": {"datetime": "2018-01-02 15:05:00", "open": 10449.1, "high": 10452.2, "low": 10442.7999, "close": 10446.8999, "csv_row": 144}, "logic": "spinning_top = body < total_range * 0.3 and both_shadows > body", "validation": "body_size < total_range * 0.3"}, {"claim": "inverted hammer at 3:25", "data": {"datetime": "2018-01-02 15:25:00", "open": 10439.0, "high": 10444.0, "low": 10437.8999, "close": 10440.1, "csv_row": 148}, "logic": "inverted_hammer = upper_shadow > body * 2", "validation": "upper_shadow > body_size * 2"}], "chart_patterns": [], "support_resistance": [{"claim": "10:05 close acted as support, tested at 2:15", "data": {"support_level": 10412.3999, "test_candle": {"datetime": "2018-01-02 14:15:00", "open": 10423.3999, "high": 10426.2, "low": 10411.8999, "close": 10413.2999, "csv_row": 134}}, "logic": "support_test = candle_low <= support <= candle_high", "validation": "level_acts_as_support"}]}, "programming_logic": {"gap_calculation": "open_price - 10435.0", "w_pattern": "double_bottom_with_higher_second_low", "bullish_engulfing": "second_candle_body_engulfs_first", "inverted_h_and_s": "left_shoulder > head < right_shoulder", "support_test": "candle_low <= support_level <= candle_high and close > support_level", "sr_interchange": "level_acts_as_support_then_resistance_then_support", "supply_zone": "area_where_selling_pressure_emerges", "demand_zone": "area_where_buying_pressure_emerges"}, "validation_rules": {}, "csv_mapping": {"rows_to_fill": [{"csv_row": 74, "observation_type": "gap_analysis", "claim": "opened gapup around 45 points"}, {"csv_row": 74, "observation_type": "opening_pattern", "claim": "inverted hanging man with long upper shadow"}, {"csv_row": null, "observation_type": "trend_phase", "claim": "bearish stance till 10:05 candle"}, {"csv_row": null, "observation_type": "reversal_pattern", "claim": "bullish engulfing at 10:05-10:10"}, {"csv_row": null, "observation_type": "trend_phase", "claim": "goes upside till 10:45"}, {"csv_row": 138, "observation_type": "candlestick_pattern", "claim": "<PERSON> at 2:35"}, {"csv_row": 144, "observation_type": "candlestick_pattern", "claim": "red spinning top at 3:05"}, {"csv_row": 148, "observation_type": "candlestick_pattern", "claim": "inverted hammer at 3:25"}, {"csv_row": null, "observation_type": "support_resistance", "claim": "10:05 close acted as support, tested at 2:15"}], "column_mappings": {"Observation ID": "observation_id", "Market Condition": "trend_direction", "Pattern Start Time": "start_time", "Pattern End Time": "end_time", "Pattern Name": "pattern_name", "Nature of Pattern": "pattern_type", "Your Reasoning": "original_claim", "Expected Outcome": "predicted_result"}}}}}}