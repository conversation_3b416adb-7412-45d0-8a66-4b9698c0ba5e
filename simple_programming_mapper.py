"""
Simple Programming Mapper
Takes your analysis as ground truth and converts to programming terms for AI training
"""

import pandas as pd
import json
from datetime import datetime

class SimpleProgrammingMapper:
    def __init__(self, csv_path: str = "Nifty.csv"):
        self.df = pd.read_csv(csv_path, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        
    def get_candle_at_time(self, date: str, time: str) -> dict:
        """Get exact candle data for specific time"""
        target_dt = pd.to_datetime(f"{date} {time}")
        time_diff = abs(self.df['datetime'] - target_dt)
        closest_idx = time_diff.idxmin()
        candle = self.df.iloc[closest_idx]
        
        return {
            'datetime': str(candle['datetime']),
            'open': float(candle['open']),
            'high': float(candle['high']),
            'low': float(candle['low']),
            'close': float(candle['close']),
            'csv_row': int(closest_idx)
        }
    
    def get_range_data(self, date: str, start_time: str, end_time: str) -> dict:
        """Get data for time range"""
        start_dt = pd.to_datetime(f"{date} {start_time}")
        end_dt = pd.to_datetime(f"{date} {end_time}")
        
        mask = (self.df['datetime'] >= start_dt) & (self.df['datetime'] <= end_dt)
        range_data = self.df[mask].copy()
        
        if range_data.empty:
            return {'error': 'No data found'}
        
        return {
            'start_price': float(range_data.iloc[0]['close']),
            'end_price': float(range_data.iloc[-1]['close']),
            'high': float(range_data['high'].max()),
            'low': float(range_data['low'].min()),
            'net_move': float(range_data.iloc[-1]['close'] - range_data.iloc[0]['close']),
            'candle_count': len(range_data),
            'csv_rows': range_data.index.tolist()
        }
    
    def convert_jan_1_analysis(self) -> dict:
        """Convert Jan 1 analysis to programming terms"""
        
        date = "2018-01-01"
        
        # Your observations mapped to programming terms
        programming_data = {
            'observation_id': 'JAN_01_2018_PROGRAMMING',
            'date': date,
            'your_analysis': "Market opens 10 points gap up, sideways to bearish till 1:20, bullish 1:25-2:20, bearish from 2:25, inverted hammer 2:40, hanging man 2:45, hanging man bottom 3:05, parallel channel 9:25-2:45, channel break 2:50 with big IFC",
            
            # Programming formulas for your observations
            'programming_formulas': {
                'gap_calculation': 'open_price - previous_close',
                'trend_direction': 'end_price - start_price',
                'trend_strength': 'abs(price_change) / start_price * 100',
                'inverted_hammer': 'upper_shadow > body_size * 2 and lower_shadow < body_size',
                'hanging_man': 'lower_shadow > body_size * 2 and upper_shadow < body_size',
                'channel_break': 'candle_low < channel_support or candle_high > channel_resistance',
                'big_candle': 'candle_range > average_range * 1.5'
            },
            
            # Mapped observations with actual data
            'mapped_observations': [
                {
                    'claim': 'Market opens around 10 points gap up from yesterday close 10467.40',
                    'type': 'gap_analysis',
                    'data': self.get_candle_at_time(date, '09:15'),
                    'programming_logic': 'gap_points = open_price - 10467.40',
                    'validation_formula': 'abs(gap_points - 10) <= 5'
                },
                {
                    'claim': 'remained sideways to bearish till the close of 1:20',
                    'type': 'trend_phase',
                    'data': self.get_range_data(date, '09:15', '13:20'),
                    'programming_logic': 'trend = "bearish" if end_price < start_price else "bullish"',
                    'validation_formula': 'net_move < 0'
                },
                {
                    'claim': 'from 1:25 it give one bullish move till 2:20 close',
                    'type': 'trend_phase',
                    'data': self.get_range_data(date, '13:25', '14:20'),
                    'programming_logic': 'bullish_move = end_price > start_price',
                    'validation_formula': 'net_move > 0'
                },
                {
                    'claim': 'from 2:25 candle it started bearish move',
                    'type': 'trend_phase',
                    'data': self.get_range_data(date, '14:25', '15:25'),
                    'programming_logic': 'bearish_move = end_price < start_price',
                    'validation_formula': 'net_move < 0'
                },
                {
                    'claim': 'inverted hammer at 2:40',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '14:40'),
                    'programming_logic': 'upper_shadow = high - max(open, close); body = abs(close - open)',
                    'validation_formula': 'upper_shadow > body * 2'
                },
                {
                    'claim': 'hanging man at 2:45',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '14:45'),
                    'programming_logic': 'lower_shadow = min(open, close) - low; body = abs(close - open)',
                    'validation_formula': 'lower_shadow > body * 2'
                },
                {
                    'claim': 'hanging man at 3:05 (bottom)',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '15:05'),
                    'programming_logic': 'is_bottom = candle_low == day_low',
                    'validation_formula': 'lower_shadow > body * 2 and is_bottom'
                },
                {
                    'claim': 'parallel channel from 9:25 to 2:45',
                    'type': 'chart_pattern',
                    'data': self.get_range_data(date, '09:25', '14:45'),
                    'programming_logic': 'channel_top = max(highs); channel_bottom = min(lows)',
                    'validation_formula': 'price_oscillates_between_levels'
                },
                {
                    'claim': '2:50 candle breaks parallel channel with big IFC',
                    'type': 'pattern_break',
                    'data': self.get_candle_at_time(date, '14:50'),
                    'programming_logic': 'candle_range = high - low; avg_range = average_candle_range',
                    'validation_formula': 'candle_range > avg_range * 1.5'
                }
            ]
        }
        
        return programming_data
    
    def convert_jan_2_analysis(self) -> dict:
        """Convert Jan 2 analysis to programming terms"""
        
        date = "2018-01-02"
        
        # Get Jan 1 close for gap calculation
        jan_1_data = self.df[self.df['datetime'].dt.date == pd.to_datetime('2018-01-01').date()]
        jan_1_close = float(jan_1_data.iloc[-1]['close']) if not jan_1_data.empty else 10435.0
        
        programming_data = {
            'observation_id': 'JAN_02_2018_PROGRAMMING',
            'date': date,
            'your_analysis': "Market formed W pattern previous day 3:00-3:25, opened gap up 45 points with inverted hanging man, bearish till 10:05, bullish engulfing 10:05-10:10, upside till 10:45, inverted H&S 9:40-10:25, 10:05 close as support tested at 2:15, supply zones, demand zones, green hammer 2:35, red spinning top 3:05, inverted hammer 3:25, SR interchange from Jan 1 3:20 wick",
            
            'programming_formulas': {
                'gap_calculation': f'open_price - {jan_1_close}',
                'w_pattern': 'double_bottom_with_higher_second_low',
                'bullish_engulfing': 'second_candle_body_engulfs_first',
                'inverted_h_and_s': 'left_shoulder > head < right_shoulder',
                'support_test': 'candle_low <= support_level <= candle_high and close > support_level',
                'sr_interchange': 'level_acts_as_support_then_resistance_then_support',
                'supply_zone': 'area_where_selling_pressure_emerges',
                'demand_zone': 'area_where_buying_pressure_emerges'
            },
            
            'mapped_observations': [
                {
                    'claim': 'opened gapup around 45 points',
                    'type': 'gap_analysis',
                    'data': self.get_candle_at_time(date, '09:15'),
                    'programming_logic': f'gap_points = open_price - {jan_1_close}',
                    'validation_formula': 'abs(gap_points - 45) <= 5'
                },
                {
                    'claim': 'inverted hanging man with long upper shadow',
                    'type': 'opening_pattern',
                    'data': self.get_candle_at_time(date, '09:15'),
                    'programming_logic': 'upper_shadow = high - max(open, close)',
                    'validation_formula': 'upper_shadow > body_size * 3'
                },
                {
                    'claim': 'bearish stance till 10:05 candle',
                    'type': 'trend_phase',
                    'data': self.get_range_data(date, '09:15', '10:05'),
                    'programming_logic': 'bearish_trend = end_price < start_price',
                    'validation_formula': 'net_move < 0'
                },
                {
                    'claim': 'bullish engulfing at 10:05-10:10',
                    'type': 'reversal_pattern',
                    'data': {
                        'first_candle': self.get_candle_at_time(date, '10:05'),
                        'second_candle': self.get_candle_at_time(date, '10:10')
                    },
                    'programming_logic': 'engulfing = second_open < first_close and second_close > first_open',
                    'validation_formula': 'pattern_validates_engulfing_rules'
                },
                {
                    'claim': 'goes upside till 10:45',
                    'type': 'trend_phase',
                    'data': self.get_range_data(date, '10:10', '10:45'),
                    'programming_logic': 'upside_move = end_price > start_price',
                    'validation_formula': 'net_move > 0'
                },
                {
                    'claim': 'Green Hammer at 2:35',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '14:35'),
                    'programming_logic': 'hammer = lower_shadow > body * 2 and close > open',
                    'validation_formula': 'lower_shadow > body_size * 2 and close > open'
                },
                {
                    'claim': 'red spinning top at 3:05',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '15:05'),
                    'programming_logic': 'spinning_top = body < total_range * 0.3 and both_shadows > body',
                    'validation_formula': 'body_size < total_range * 0.3'
                },
                {
                    'claim': 'inverted hammer at 3:25',
                    'type': 'candlestick_pattern',
                    'data': self.get_candle_at_time(date, '15:25'),
                    'programming_logic': 'inverted_hammer = upper_shadow > body * 2',
                    'validation_formula': 'upper_shadow > body_size * 2'
                },
                {
                    'claim': '10:05 close acted as support, tested at 2:15',
                    'type': 'support_resistance',
                    'data': {
                        'support_level': self.get_candle_at_time(date, '10:05')['close'],
                        'test_candle': self.get_candle_at_time(date, '14:15')
                    },
                    'programming_logic': 'support_test = candle_low <= support <= candle_high',
                    'validation_formula': 'level_acts_as_support'
                }
            ]
        }
        
        return programming_data
    
    def create_ai_training_structure(self, programming_data: dict) -> dict:
        """Create AI training structure from programming data"""
        
        ai_structure = {
            'training_data': {
                'observation_id': programming_data['observation_id'],
                'date': programming_data['date'],
                'analyst_methodology': programming_data['your_analysis'],
                
                'feature_extraction': {
                    'gap_analysis': [],
                    'trend_phases': [],
                    'candlestick_patterns': [],
                    'chart_patterns': [],
                    'support_resistance': []
                },
                
                'programming_logic': programming_data['programming_formulas'],
                
                'validation_rules': {},
                
                'csv_mapping': {
                    'rows_to_fill': [],
                    'column_mappings': {
                        'Observation ID': 'observation_id',
                        'Market Condition': 'trend_direction',
                        'Pattern Start Time': 'start_time',
                        'Pattern End Time': 'end_time',
                        'Pattern Name': 'pattern_name',
                        'Nature of Pattern': 'pattern_type',
                        'Your Reasoning': 'original_claim',
                        'Expected Outcome': 'predicted_result'
                    }
                }
            }
        }
        
        # Process each observation
        for obs in programming_data['mapped_observations']:
            obs_type = obs['type']
            
            if obs_type == 'gap_analysis':
                ai_structure['training_data']['feature_extraction']['gap_analysis'].append({
                    'claim': obs['claim'],
                    'data': obs['data'],
                    'logic': obs['programming_logic'],
                    'validation': obs['validation_formula']
                })
            
            elif obs_type == 'trend_phase':
                ai_structure['training_data']['feature_extraction']['trend_phases'].append({
                    'claim': obs['claim'],
                    'data': obs['data'],
                    'logic': obs['programming_logic'],
                    'validation': obs['validation_formula']
                })
            
            elif obs_type == 'candlestick_pattern':
                ai_structure['training_data']['feature_extraction']['candlestick_patterns'].append({
                    'claim': obs['claim'],
                    'data': obs['data'],
                    'logic': obs['programming_logic'],
                    'validation': obs['validation_formula']
                })
            
            elif obs_type in ['chart_pattern', 'pattern_break']:
                ai_structure['training_data']['feature_extraction']['chart_patterns'].append({
                    'claim': obs['claim'],
                    'data': obs['data'],
                    'logic': obs['programming_logic'],
                    'validation': obs['validation_formula']
                })
            
            elif obs_type == 'support_resistance':
                ai_structure['training_data']['feature_extraction']['support_resistance'].append({
                    'claim': obs['claim'],
                    'data': obs['data'],
                    'logic': obs['programming_logic'],
                    'validation': obs['validation_formula']
                })
            
            # Add CSV row mapping if data contains csv_row
            if 'csv_row' in str(obs['data']):
                ai_structure['training_data']['csv_mapping']['rows_to_fill'].append({
                    'csv_row': obs['data'].get('csv_row') if isinstance(obs['data'], dict) else None,
                    'observation_type': obs_type,
                    'claim': obs['claim']
                })
        
        return ai_structure

def main():
    """Main function to convert analysis to programming terms"""
    
    print("🤖 CONVERTING YOUR ANALYSIS TO PROGRAMMING TERMS")
    print("=" * 60)
    print("📋 Treating your analysis as ground truth")
    print("🔗 Mapping to CSV data with exact correlations")
    print()
    
    mapper = SimpleProgrammingMapper()
    
    # Convert both days
    jan_1_programming = mapper.convert_jan_1_analysis()
    jan_2_programming = mapper.convert_jan_2_analysis()
    
    # Create AI training structures
    jan_1_ai = mapper.create_ai_training_structure(jan_1_programming)
    jan_2_ai = mapper.create_ai_training_structure(jan_2_programming)
    
    # Combine results
    complete_results = {
        'metadata': {
            'created_at': datetime.now().isoformat(),
            'purpose': 'Convert natural language analysis to programming terms for AI training',
            'approach': 'Treat user analysis as ground truth, map to CSV data',
            'total_observations': len(jan_1_programming['mapped_observations']) + len(jan_2_programming['mapped_observations'])
        },
        'jan_1_2018': {
            'programming_data': jan_1_programming,
            'ai_training_structure': jan_1_ai
        },
        'jan_2_2018': {
            'programming_data': jan_2_programming,
            'ai_training_structure': jan_2_ai
        }
    }
    
    # Save results
    with open('programming_analysis_mapping.json', 'w') as f:
        json.dump(complete_results, f, indent=2, default=str)
    
    print("✅ CONVERSION COMPLETED SUCCESSFULLY")
    print(f"📊 Jan 1: {len(jan_1_programming['mapped_observations'])} observations mapped")
    print(f"📊 Jan 2: {len(jan_2_programming['mapped_observations'])} observations mapped")
    print(f"🤖 AI training structures created for both days")
    print(f"💾 Results saved to 'programming_analysis_mapping.json'")
    
    # Show sample programming terms
    print(f"\n🔍 SAMPLE PROGRAMMING MAPPINGS:")
    print(f"Gap Analysis: {jan_1_programming['programming_formulas']['gap_calculation']}")
    print(f"Trend Direction: {jan_1_programming['programming_formulas']['trend_direction']}")
    print(f"Pattern Validation: {jan_1_programming['programming_formulas']['inverted_hammer']}")
    
    print(f"\n📋 CSV CORRELATION SUMMARY:")
    jan_1_csv_rows = [obs['data'].get('csv_row') for obs in jan_1_programming['mapped_observations'] if isinstance(obs['data'], dict) and 'csv_row' in obs['data']]
    jan_2_csv_rows = [obs['data'].get('csv_row') for obs in jan_2_programming['mapped_observations'] if isinstance(obs['data'], dict) and 'csv_row' in obs['data']]
    
    print(f"Jan 1 CSV rows mapped: {len([r for r in jan_1_csv_rows if r is not None])}")
    print(f"Jan 2 CSV rows mapped: {len([r for r in jan_2_csv_rows if r is not None])}")
    
    return complete_results

if __name__ == "__main__":
    results = main()
